// ColorNinja Background Script

class ColorNinjaBackground {
    constructor() {
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Handle extension installation
        chrome.runtime.onInstalled.addListener((details) => {
            if (details.reason === 'install') {
                console.log('ColorNinja extension installed');
                this.setDefaultSettings();
            } else if (details.reason === 'update') {
                console.log('ColorNinja extension updated');
            }
        });

        // Handle messages from popup and content scripts
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
            return true; // Keep the message channel open for async responses
        });

        // Handle tab updates to manage tab-specific data
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            if (changeInfo.status === 'complete') {
                this.handleTabUpdate(tabId, tab);
            }
        });

        // Handle tab removal to clean up data
        chrome.tabs.onRemoved.addListener((tabId, removeInfo) => {
            this.cleanupTabData(tabId);
        });
    }

    setDefaultSettings() {
        // Set default theme and other settings
        chrome.storage.local.set({
            theme: 'light',
            autoExtract: false,
            maxColors: 50
        });
    }

    async handleMessage(request, sender, sendResponse) {
        try {
            switch (request.action) {
                case 'getTabColors':
                    const colors = await this.getTabColors(sender.tab.id);
                    sendResponse({ colors: colors });
                    break;

                case 'saveTabColors':
                    await this.saveTabColors(sender.tab.id, request.colors);
                    sendResponse({ success: true });
                    break;

                case 'clearTabColors':
                    await this.clearTabColors(sender.tab.id);
                    sendResponse({ success: true });
                    break;

                case 'getSettings':
                    const settings = await this.getSettings();
                    sendResponse({ settings: settings });
                    break;

                case 'saveSettings':
                    await this.saveSettings(request.settings);
                    sendResponse({ success: true });
                    break;

                case 'colorPicked':
                    // Forward color picked message to popup if it's open
                    this.forwardToPopup(request);
                    sendResponse({ success: true });
                    break;

                default:
                    console.log('Unknown action:', request.action);
                    sendResponse({ error: 'Unknown action' });
            }
        } catch (error) {
            console.error('Error handling message:', error);
            sendResponse({ error: error.message });
        }
    }

    async getTabColors(tabId) {
        try {
            const result = await chrome.storage.local.get([`tab_${tabId}_colors`]);
            return result[`tab_${tabId}_colors`] || [];
        } catch (error) {
            console.error('Error getting tab colors:', error);
            return [];
        }
    }

    async saveTabColors(tabId, colors) {
        try {
            await chrome.storage.local.set({
                [`tab_${tabId}_colors`]: colors,
                [`tab_${tabId}_timestamp`]: Date.now()
            });
        } catch (error) {
            console.error('Error saving tab colors:', error);
        }
    }

    async clearTabColors(tabId) {
        try {
            await chrome.storage.local.remove([
                `tab_${tabId}_colors`,
                `tab_${tabId}_timestamp`
            ]);
        } catch (error) {
            console.error('Error clearing tab colors:', error);
        }
    }

    async getSettings() {
        try {
            const result = await chrome.storage.local.get([
                'theme',
                'autoExtract',
                'maxColors'
            ]);
            
            return {
                theme: result.theme || 'light',
                autoExtract: result.autoExtract || false,
                maxColors: result.maxColors || 50
            };
        } catch (error) {
            console.error('Error getting settings:', error);
            return {
                theme: 'light',
                autoExtract: false,
                maxColors: 50
            };
        }
    }

    async saveSettings(settings) {
        try {
            await chrome.storage.local.set(settings);
        } catch (error) {
            console.error('Error saving settings:', error);
        }
    }

    handleTabUpdate(tabId, tab) {
        // Clean up old tab data if URL changed significantly
        if (tab.url) {
            this.cleanupOldTabData();
        }
    }

    cleanupTabData(tabId) {
        // Remove tab-specific data when tab is closed
        chrome.storage.local.remove([
            `tab_${tabId}_colors`,
            `tab_${tabId}_timestamp`
        ]);
    }

    async cleanupOldTabData() {
        try {
            // Clean up tab data older than 24 hours
            const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);
            const result = await chrome.storage.local.get();
            
            const keysToRemove = [];
            
            for (const key in result) {
                if (key.startsWith('tab_') && key.endsWith('_timestamp')) {
                    if (result[key] < oneDayAgo) {
                        const tabId = key.replace('tab_', '').replace('_timestamp', '');
                        keysToRemove.push(`tab_${tabId}_colors`);
                        keysToRemove.push(`tab_${tabId}_timestamp`);
                    }
                }
            }
            
            if (keysToRemove.length > 0) {
                await chrome.storage.local.remove(keysToRemove);
                console.log('Cleaned up old tab data:', keysToRemove.length / 2, 'tabs');
            }
        } catch (error) {
            console.error('Error cleaning up old tab data:', error);
        }
    }

    forwardToPopup(message) {
        // Try to send message to popup if it's open
        chrome.runtime.sendMessage(message).catch(() => {
            // Popup is not open, ignore the error
        });
    }

    // Utility method to inject content script if needed
    async ensureContentScript(tabId) {
        try {
            // Check if content script is already injected
            const response = await chrome.tabs.sendMessage(tabId, { action: 'ping' });
            return true;
        } catch (error) {
            // Content script not injected, inject it
            try {
                await chrome.scripting.executeScript({
                    target: { tabId: tabId },
                    files: ['content.js']
                });
                return true;
            } catch (injectionError) {
                console.error('Error injecting content script:', injectionError);
                return false;
            }
        }
    }
}

// Initialize the background script
new ColorNinjaBackground();

