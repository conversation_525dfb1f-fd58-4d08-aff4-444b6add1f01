<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ColorNinja Debug Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        
        .debug-info {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-colors {
            display: flex;
            gap: 10px;
            margin: 20px 0;
        }
        
        .color-sample {
            width: 50px;
            height: 50px;
            border-radius: 4px;
            border: 2px solid #333;
        }
        
        .red { background: #ff0000; }
        .green { background: #00ff00; }
        .blue { background: #0000ff; }
        .yellow { background: #ffff00; }
        .purple { background: #800080; }
        
        .instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #2196f3;
        }
        
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="debug-info">
        <h1>ColorNinja Debug Test Page</h1>
        
        <div class="instructions">
            <h3>Testing Instructions:</h3>
            <ol>
                <li>Make sure the ColorNinja extension is loaded and enabled</li>
                <li>Click the ColorNinja icon in your browser toolbar</li>
                <li>Try the "Extract Colors" button - it should find the colors below</li>
                <li>Try the "Color Picker" button - it should let you pick any color</li>
                <li>Check the browser console (F12) for any error messages</li>
            </ol>
        </div>
        
        <div class="status info">
            <strong>Browser Support Check:</strong>
            <div id="browser-support"></div>
        </div>
        
        <div class="test-colors">
            <div class="color-sample red" title="Red: #ff0000"></div>
            <div class="color-sample green" title="Green: #00ff00"></div>
            <div class="color-sample blue" title="Blue: #0000ff"></div>
            <div class="color-sample yellow" title="Yellow: #ffff00"></div>
            <div class="color-sample purple" title="Purple: #800080"></div>
        </div>
        
        <div class="status" id="test-status">
            Ready for testing...
        </div>
        
        <h3>Expected Results:</h3>
        <ul>
            <li>Extract Colors should find: #ff0000, #00ff00, #0000ff, #ffff00, #800080, and other page colors</li>
            <li>Color Picker should work without errors (if supported)</li>
            <li>No console errors should appear</li>
            <li>All buttons should be responsive</li>
        </ul>
        
        <h3>Common Issues to Check:</h3>
        <ul>
            <li>If "Service worker registration failed" appears, reload the extension</li>
            <li>If "Cannot access chrome:// URL" appears, make sure you're on this HTML page, not a chrome:// page</li>
            <li>If color picker doesn't work, check if your browser supports EyeDropper API</li>
            <li>If no colors are extracted, check the browser console for errors</li>
        </ul>
    </div>

    <script>
        // Check browser support
        function checkBrowserSupport() {
            const supportDiv = document.getElementById('browser-support');
            const statusDiv = document.getElementById('test-status');
            
            let supportInfo = [];
            
            // Check EyeDropper API
            if (window.EyeDropper) {
                supportInfo.push('✅ EyeDropper API supported');
            } else {
                supportInfo.push('❌ EyeDropper API not supported');
            }
            
            // Check Clipboard API
            if (navigator.clipboard) {
                supportInfo.push('✅ Clipboard API supported');
            } else {
                supportInfo.push('❌ Clipboard API not supported');
            }
            
            // Check Chrome Extensions API
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                supportInfo.push('✅ Chrome Extensions API available');
            } else {
                supportInfo.push('❌ Chrome Extensions API not available');
            }
            
            supportDiv.innerHTML = supportInfo.join('<br>');
            
            // Update status
            if (window.EyeDropper && navigator.clipboard) {
                statusDiv.className = 'status success';
                statusDiv.textContent = '✅ Browser fully supports ColorNinja features';
            } else {
                statusDiv.className = 'status error';
                statusDiv.textContent = '⚠️ Some features may not work in this browser';
            }
        }
        
        // Run checks when page loads
        document.addEventListener('DOMContentLoaded', checkBrowserSupport);
        
        // Log page load for debugging
        console.log('ColorNinja Debug Test Page loaded');
        console.log('Current URL:', window.location.href);
        console.log('EyeDropper supported:', !!window.EyeDropper);
        console.log('Clipboard API supported:', !!navigator.clipboard);
    </script>
</body>
</html>
