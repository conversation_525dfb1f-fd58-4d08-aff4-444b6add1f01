{"manifest_version": 3, "name": "ColorNinja", "version": "1.0.0", "description": "Extract and interact with all colors used on any webpage. Pick colors, save them, and download color data.", "permissions": ["activeTab", "scripting", "storage"], "action": {"default_popup": "popup.html", "default_title": "ColorNinja", "default_icon": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "background": {"service_worker": "background.js", "type": "module"}, "content_scripts": [{"matches": ["http://*/*", "https://*/*"], "js": ["content.js"], "run_at": "document_idle", "all_frames": false}], "icons": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "host_permissions": ["http://*/*", "https://*/*"]}