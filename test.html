<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ColorNinja Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .color-boxes {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .color-box {
            height: 100px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
        
        .red { background-color: #e74c3c; }
        .blue { background-color: #3498db; }
        .green { background-color: #2ecc71; }
        .yellow { background-color: #f1c40f; color: #333; }
        .purple { background-color: #9b59b6; }
        .orange { background-color: #e67e22; }
        
        .text-colors {
            margin: 30px 0;
        }
        
        .text-red { color: #c0392b; }
        .text-blue { color: #2980b9; }
        .text-green { color: #27ae60; }
        
        .border-example {
            border: 5px solid #34495e;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        
        .gradient-text {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
        }
        
        .instructions {
            background: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #3498db;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>ColorNinja Test Page</h1>
        
        <div class="instructions">
            <h3>How to test ColorNinja:</h3>
            <ol>
                <li>Click the ColorNinja extension icon in your browser toolbar</li>
                <li>Click "Extract Colors" to see all colors used on this page</li>
                <li>Click "Color Picker" to pick any color from the page</li>
                <li>Save colors you like and download them as JSON</li>
            </ol>
        </div>
        
        <div class="color-boxes">
            <div class="color-box red">#E74C3C</div>
            <div class="color-box blue">#3498DB</div>
            <div class="color-box green">#2ECC71</div>
            <div class="color-box yellow">#F1C40F</div>
            <div class="color-box purple">#9B59B6</div>
            <div class="color-box orange">#E67E22</div>
        </div>
        
        <div class="text-colors">
            <p class="text-red">This text is red (#C0392B)</p>
            <p class="text-blue">This text is blue (#2980B9)</p>
            <p class="text-green">This text is green (#27AE60)</p>
        </div>
        
        <div class="border-example">
            This box has a dark border (#34495E)
        </div>
        
        <div class="gradient-text">
            Gradient Text with Multiple Colors
        </div>
        
        <p>This page contains various colors that ColorNinja should be able to extract:</p>
        <ul>
            <li>Background colors</li>
            <li>Text colors</li>
            <li>Border colors</li>
            <li>Gradient colors</li>
        </ul>
    </div>
</body>
</html>
