// ColorNinja Content Script

class ColorExtractor {
    constructor() {
        this.colorMap = new Map();
        this.setupMessageListener();
    }

    setupMessageListener() {
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            if (request.action === 'extractColors') {
                const colors = this.extractColors();
                sendResponse({ colors: colors });
            } else if (request.action === 'activateColorPicker') {
                this.activateColorPicker();
                sendResponse({ success: true });
            }
        });
    }

    extractColors() {
        this.colorMap.clear();
        
        // Extract colors from all elements
        this.extractFromElements();
        
        // Extract colors from stylesheets
        this.extractFromStylesheets();
        
        // Convert map to array and sort by count
        const colorsArray = Array.from(this.colorMap.entries()).map(([color, count]) => {
            const colorData = this.formatColorData(color);
            return {
                ...colorData,
                count: count
            };
        });
        
        // Sort by count (descending)
        colorsArray.sort((a, b) => b.count - a.count);
        
        return colorsArray;
    }

    extractFromElements() {
        const elements = document.querySelectorAll('*');
        
        elements.forEach(element => {
            const computedStyle = window.getComputedStyle(element);
            
            // Extract various color properties
            const colorProperties = [
                'color',
                'background-color',
                'border-color',
                'border-top-color',
                'border-right-color',
                'border-bottom-color',
                'border-left-color',
                'outline-color',
                'text-decoration-color',
                'column-rule-color',
                'caret-color'
            ];
            
            colorProperties.forEach(property => {
                const value = computedStyle.getPropertyValue(property);
                if (value && value !== 'rgba(0, 0, 0, 0)' && value !== 'transparent') {
                    const normalizedColor = this.normalizeColor(value);
                    if (normalizedColor && this.isValidColor(normalizedColor)) {
                        this.addColor(normalizedColor);
                    }
                }
            });
            
            // Extract background-image colors (gradients)
            const backgroundImage = computedStyle.getPropertyValue('background-image');
            if (backgroundImage && backgroundImage !== 'none') {
                this.extractColorsFromGradient(backgroundImage);
            }
        });
    }

    extractFromStylesheets() {
        try {
            // Extract from inline styles
            const elementsWithStyle = document.querySelectorAll('[style]');
            elementsWithStyle.forEach(element => {
                const style = element.getAttribute('style');
                this.extractColorsFromCSSText(style);
            });
            
            // Extract from style elements
            const styleElements = document.querySelectorAll('style');
            styleElements.forEach(styleElement => {
                this.extractColorsFromCSSText(styleElement.textContent);
            });
            
            // Extract from external stylesheets (same-origin only)
            for (let i = 0; i < document.styleSheets.length; i++) {
                try {
                    const styleSheet = document.styleSheets[i];
                    if (styleSheet.cssRules) {
                        this.extractFromCSSRules(styleSheet.cssRules);
                    }
                } catch (e) {
                    // Cross-origin stylesheets will throw an error, skip them
                    console.log('Skipping cross-origin stylesheet');
                }
            }
        } catch (error) {
            console.error('Error extracting from stylesheets:', error);
        }
    }

    extractFromCSSRules(cssRules) {
        for (let i = 0; i < cssRules.length; i++) {
            const rule = cssRules[i];
            
            if (rule.style) {
                // Extract from rule style
                for (let j = 0; j < rule.style.length; j++) {
                    const property = rule.style[j];
                    const value = rule.style.getPropertyValue(property);
                    
                    if (this.isColorProperty(property)) {
                        const normalizedColor = this.normalizeColor(value);
                        if (normalizedColor && this.isValidColor(normalizedColor)) {
                            this.addColor(normalizedColor);
                        }
                    }
                }
            }
            
            // Handle nested rules (media queries, etc.)
            if (rule.cssRules) {
                this.extractFromCSSRules(rule.cssRules);
            }
        }
    }

    extractColorsFromCSSText(cssText) {
        if (!cssText) return;
        
        // Regular expressions for different color formats
        const colorRegexes = [
            /#([0-9a-fA-F]{3}|[0-9a-fA-F]{6})\b/g, // Hex colors
            /rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/g, // RGB colors
            /rgba\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([0-9.]+)\s*\)/g, // RGBA colors
            /hsl\(\s*(\d+)\s*,\s*(\d+)%\s*,\s*(\d+)%\s*\)/g, // HSL colors
            /hsla\(\s*(\d+)\s*,\s*(\d+)%\s*,\s*(\d+)%\s*,\s*([0-9.]+)\s*\)/g // HSLA colors
        ];
        
        colorRegexes.forEach(regex => {
            let match;
            while ((match = regex.exec(cssText)) !== null) {
                const normalizedColor = this.normalizeColor(match[0]);
                if (normalizedColor && this.isValidColor(normalizedColor)) {
                    this.addColor(normalizedColor);
                }
            }
        });
    }

    extractColorsFromGradient(gradientString) {
        // Extract colors from CSS gradients
        const colorRegex = /(#[0-9a-fA-F]{3,6}|rgb\([^)]+\)|rgba\([^)]+\)|hsl\([^)]+\)|hsla\([^)]+\))/g;
        let match;
        
        while ((match = colorRegex.exec(gradientString)) !== null) {
            const normalizedColor = this.normalizeColor(match[0]);
            if (normalizedColor && this.isValidColor(normalizedColor)) {
                this.addColor(normalizedColor);
            }
        }
    }

    isColorProperty(property) {
        const colorProperties = [
            'color', 'background-color', 'border-color', 'border-top-color',
            'border-right-color', 'border-bottom-color', 'border-left-color',
            'outline-color', 'text-decoration-color', 'column-rule-color',
            'caret-color', 'background', 'border', 'box-shadow', 'text-shadow'
        ];
        
        return colorProperties.some(prop => property.includes(prop));
    }

    normalizeColor(colorString) {
        if (!colorString || colorString === 'transparent' || colorString === 'inherit' || colorString === 'initial') {
            return null;
        }
        
        // Create a temporary element to normalize the color
        const tempElement = document.createElement('div');
        tempElement.style.color = colorString;
        document.body.appendChild(tempElement);
        
        const computedColor = window.getComputedStyle(tempElement).color;
        document.body.removeChild(tempElement);
        
        // Convert to hex
        return this.rgbToHex(computedColor);
    }

    rgbToHex(rgb) {
        if (!rgb || rgb === 'rgba(0, 0, 0, 0)') return null;
        
        const rgbMatch = rgb.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*([0-9.]+))?\)/);
        if (!rgbMatch) return null;
        
        const r = parseInt(rgbMatch[1]);
        const g = parseInt(rgbMatch[2]);
        const b = parseInt(rgbMatch[3]);
        const a = rgbMatch[4] ? parseFloat(rgbMatch[4]) : 1;
        
        // Skip transparent colors
        if (a < 0.1) return null;
        
        return '#' + [r, g, b].map(x => {
            const hex = x.toString(16);
            return hex.length === 1 ? '0' + hex : hex;
        }).join('');
    }

    isValidColor(hex) {
        return /^#[0-9a-fA-F]{6}$/.test(hex);
    }

    addColor(color) {
        if (this.colorMap.has(color)) {
            this.colorMap.set(color, this.colorMap.get(color) + 1);
        } else {
            this.colorMap.set(color, 1);
        }
    }

    formatColorData(hex) {
        const rgb = this.hexToRgb(hex);
        if (!rgb) return null;
        
        const hsl = this.rgbToHsl(rgb.r, rgb.g, rgb.b);
        
        return {
            hex: hex.toUpperCase(),
            rgb: `rgb(${rgb.r}, ${rgb.g}, ${rgb.b})`,
            hsl: `hsl(${hsl.h}, ${hsl.s}%, ${hsl.l}%)`
        };
    }

    hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    }

    rgbToHsl(r, g, b) {
        r /= 255;
        g /= 255;
        b /= 255;
        
        const max = Math.max(r, g, b);
        const min = Math.min(r, g, b);
        let h, s, l = (max + min) / 2;
        
        if (max === min) {
            h = s = 0;
        } else {
            const d = max - min;
            s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
            
            switch (max) {
                case r: h = (g - b) / d + (g < b ? 6 : 0); break;
                case g: h = (b - r) / d + 2; break;
                case b: h = (r - g) / d + 4; break;
            }
            h /= 6;
        }
        
        return {
            h: Math.round(h * 360),
            s: Math.round(s * 100),
            l: Math.round(l * 100)
        };
    }

    async activateColorPicker() {
        try {
            if (!window.EyeDropper) {
                throw new Error('EyeDropper API not supported');
            }
            
            const eyeDropper = new EyeDropper();
            const result = await eyeDropper.open();
            
            if (result && result.sRGBHex) {
                const colorData = this.formatColorData(result.sRGBHex);
                
                // Copy to clipboard
                await navigator.clipboard.writeText(result.sRGBHex);
                
                // Send color data back to popup
                chrome.runtime.sendMessage({
                    action: 'colorPicked',
                    colorData: colorData
                });
                
                // Show temporary notification
                this.showColorPickedNotification(result.sRGBHex);
            }
        } catch (error) {
            console.error('Error with color picker:', error);
            
            // Fallback notification
            this.showErrorNotification('Color picker not supported or cancelled');
        }
    }

    showColorPickedNotification(color) {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #10b981;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            z-index: 10000;
            animation: slideIn 0.3s ease;
        `;
        
        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px;">
                <div style="width: 20px; height: 20px; background: ${color}; border-radius: 4px; border: 2px solid white;"></div>
                <span>Color picked: ${color}</span>
            </div>
        `;
        
        // Add animation styles
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);
        
        document.body.appendChild(notification);
        
        // Remove notification after 3 seconds
        setTimeout(() => {
            notification.style.animation = 'slideIn 0.3s ease reverse';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
                if (style.parentNode) {
                    style.parentNode.removeChild(style);
                }
            }, 300);
        }, 3000);
    }

    showErrorNotification(message) {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #ef4444;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            z-index: 10000;
        `;
        
        notification.textContent = message;
        document.body.appendChild(notification);
        
        // Remove notification after 3 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }
}

// Initialize the color extractor
new ColorExtractor();

