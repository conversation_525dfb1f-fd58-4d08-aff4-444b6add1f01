/* CSS Variables for theming */
:root {
  --bg-primary: #ffffff;
  --bg-secondary: rgba(255, 255, 255, 0.8);
  --bg-glass: rgba(255, 255, 255, 0.1);
  --text-primary: #333333;
  --text-secondary: #666666;
  --border-color: rgba(0, 0, 0, 0.1);
  --accent-color: #4f46e5;
  --accent-hover: #3730a3;
  --success-color: #10b981;
  --danger-color: #ef4444;
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Dark theme variables */
.dark-mode {
  --bg-primary: #1f2937;
  --bg-secondary: rgba(31, 41, 55, 0.8);
  --bg-glass: rgba(255, 255, 255, 0.05);
  --text-primary: #f9fafb;
  --text-secondary: #d1d5db;
  --border-color: rgba(255, 255, 255, 0.1);
  --accent-color: #6366f1;
  --accent-hover: #4f46e5;
  --success-color: #34d399;
  --danger-color: #f87171;
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
}

/* Base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: all 0.3s ease;
  min-height: 100vh;
}

.container {
  width: 380px;
  max-height: 600px;
  padding: 20px;
  background: var(--bg-primary);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  box-shadow: var(--shadow-lg);
  overflow-y: auto;
}

/* Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-color);
}

.title {
  font-size: 24px;
  font-weight: 700;
  color: var(--accent-color);
  margin: 0;
}

.theme-toggle {
  background: var(--bg-glass);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.theme-toggle:hover {
  background: var(--bg-secondary);
  transform: scale(1.05);
}

.theme-icon {
  font-size: 16px;
  display: block;
}

/* Action buttons */
.action-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.btn-primary {
  background: var(--accent-color);
  color: white;
}

.btn-primary:hover {
  background: var(--accent-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.btn-secondary {
  background: var(--bg-glass);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background: var(--bg-secondary);
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.btn-download {
  background: var(--success-color);
  color: white;
  width: 100%;
}

.btn-download:hover {
  background: #059669;
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.btn-icon {
  font-size: 16px;
}

/* Colors sections */
.colors-section {
  margin-bottom: 20px;
}

.colors-section h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  color: var(--text-primary);
}

.colors-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 12px;
  background: var(--bg-glass);
  backdrop-filter: blur(10px);
}

.placeholder {
  color: var(--text-secondary);
  font-style: italic;
  text-align: center;
  padding: 20px;
}

.restricted-message {
  background: var(--bg-glass);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  color: var(--text-secondary);
}

.restricted-message p {
  margin-bottom: 12px;
}

.restricted-message p:first-child {
  font-weight: 600;
  color: var(--danger-color);
  font-size: 16px;
}

.restricted-message ul {
  text-align: left;
  margin: 12px 0;
  padding-left: 20px;
}

.restricted-message li {
  margin-bottom: 4px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

.btn:disabled:hover {
  transform: none !important;
  box-shadow: none !important;
}

/* Color items */
.color-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  margin-bottom: 8px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.color-item:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

.color-item:last-child {
  margin-bottom: 0;
}

.color-swatch {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  border: 2px solid var(--border-color);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.color-swatch:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow);
}

.color-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.color-hex {
  font-weight: 600;
  font-family: 'Courier New', monospace;
  color: var(--text-primary);
}

.color-count {
  font-size: 12px;
  color: var(--text-secondary);
}

.color-formats {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.format-btn {
  padding: 4px 8px;
  font-size: 11px;
  background: var(--bg-glass);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--text-secondary);
}

.format-btn:hover {
  background: var(--accent-color);
  color: white;
  transform: scale(1.05);
}

.color-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.save-btn, .remove-btn {
  padding: 6px 12px;
  font-size: 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.save-btn {
  background: var(--success-color);
  color: white;
}

.save-btn:hover {
  background: #059669;
  transform: scale(1.05);
}

.remove-btn {
  background: var(--danger-color);
  color: white;
}

.remove-btn:hover {
  background: #dc2626;
  transform: scale(1.05);
}

/* Picked color section */
.picked-color-section {
  margin-bottom: 20px;
  padding: 16px;
  background: var(--bg-glass);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.picked-color-section h3 {
  color: var(--accent-color);
  margin-bottom: 12px;
}

/* Download section */
.download-section {
  margin-bottom: 20px;
}

/* Footer */
.footer {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding-top: 15px;
  border-top: 1px solid var(--border-color);
  font-size: 12px;
  color: var(--text-secondary);
}

.heart {
  color: #ef4444;
  animation: heartbeat 1.5s ease-in-out infinite;
}

@keyframes heartbeat {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.linkedin-link {
  color: var(--text-secondary);
  text-decoration: none;
  transition: all 0.3s ease;
}

.linkedin-link:hover {
  color: #0077b5;
  transform: scale(1.1);
}

.linkedin-icon {
  font-size: 14px;
}

/* Toast notification */
.toast {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%) translateY(100px);
  background: var(--success-color);
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: var(--shadow-lg);
  opacity: 0;
  transition: all 0.3s ease;
  z-index: 1000;
}

.toast.show {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
}

/* Scrollbar styling */
.colors-list::-webkit-scrollbar {
  width: 6px;
}

.colors-list::-webkit-scrollbar-track {
  background: var(--bg-glass);
  border-radius: 3px;
}

.colors-list::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.colors-list::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

/* Responsive adjustments */
@media (max-width: 400px) {
  .container {
    width: 100%;
    max-width: 380px;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .color-formats {
    flex-direction: column;
  }
}

