# ColorNinja Chrome Extension

ColorNinja is a powerful Chrome extension designed to extract and interact with all colors used on any webpage. It provides an intuitive interface for color analysis, picking, and management.

## Features

### 🎨 Color Extraction
- Extract all visible colors from any webpage
- Analyze colors from inline styles, stylesheets, and computed styles
- Count color occurrences and sort by usage frequency
- Support for various color formats (HEX, RGB, HSL)

### 🎯 Color Picker
- Built-in EyeDropper tool for precise color picking
- Pick colors directly from any webpage
- Automatic clipboard copying of picked colors
- Visual feedback with notifications

### 💾 Color Management
- Save favorite colors for future use
- Persistent storage across browser sessions
- Tab-specific color data retention
- Easy color removal and organization

### 🌙 Theming
- Light and dark mode support
- Smooth theme transitions
- Glassmorphism design effects
- Modern, clean interface

### 📥 Export Functionality
- Download color data as JSON
- Include metadata (URL, timestamp, counts)
- Comprehensive color information export

### 🎨 User Interface
- Responsive design for various screen sizes
- Intuitive color swatches with hover effects
- Multiple format copying (HEX, RGB, HSL)
- Toast notifications for user feedback

## Installation

1. Download the ColorNinja extension ZIP file
2. Extract the contents to a folder
3. Open Chrome and navigate to `chrome://extensions/`
4. Enable "Developer mode" in the top right corner
5. Click "Load unpacked" and select the extracted folder
6. The ColorNinja extension will appear in your extensions toolbar

## Usage

### Extracting Colors
1. Navigate to any webpage
2. Click the ColorNinja extension icon
3. Click "Extract Colors" button
4. View all colors used on the page, sorted by frequency

### Using the Color Picker
1. Click the "Color Picker" button in the extension popup
2. The popup will close to avoid obstruction
3. Click anywhere on the webpage to pick a color
4. The color will be automatically copied to your clipboard
5. Reopen the extension to see the picked color and save it

### Managing Colors
- Click any color swatch to copy its HEX value
- Use format buttons (HEX, RGB, HSL) to copy in different formats
- Click "Save" to add colors to your saved collection
- Use the "×" button to remove saved colors

### Theming
- Click the theme toggle button (🌙/☀️) in the top right
- Switch between light and dark modes
- Theme preference is saved automatically

### Exporting Data
- Click "Download as JSON" to export all color data
- Includes extracted colors, saved colors, and metadata
- File is named with current date for easy organization

## Technical Details

### Permissions
- `activeTab`: Access current webpage for color extraction
- `scripting`: Inject content scripts for functionality
- `storage`: Store user preferences and tab data
- `clipboardWrite`: Copy colors to clipboard

### Browser Compatibility
- Chrome 88+ (EyeDropper API support)
- Chromium-based browsers with EyeDropper support

### File Structure
```
ColorNinja/
├── manifest.json          # Extension configuration
├── popup.html            # Main interface
├── popup.css             # Styling and themes
├── popup.js              # Popup functionality
├── content.js            # Color extraction and picker
├── background.js         # Background service worker
├── icons/                # Extension icons
│   ├── icon16.png
│   ├── icon32.png
│   ├── icon48.png
│   └── icon128.png
└── README.md             # This file
```

## Development

### Architecture
- **Popup**: Main user interface with color display and controls
- **Content Script**: Handles DOM analysis and EyeDropper functionality
- **Background Script**: Manages tab-specific data and communication
- **Storage**: Uses localStorage for saved colors and chrome.storage for tab data

### Color Extraction Process
1. Traverse all DOM elements
2. Extract computed styles for color properties
3. Parse CSS stylesheets (same-origin only)
4. Normalize colors to HEX format
5. Count occurrences and sort by frequency

### Data Flow
1. User clicks "Extract Colors"
2. Popup sends message to content script
3. Content script analyzes DOM and extracts colors
4. Color data is sent back to popup
5. Popup displays colors and saves to tab storage

## Credits

Made with ❤️ by Deepak Raj

Connect on [LinkedIn](https://www.linkedin.com/in/deepak-rajj/)

## License

This project is open source and available under the MIT License.

