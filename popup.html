<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ColorNinja</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <!-- Header with theme toggle -->
        <div class="header">
            <h1 class="title">ColorNinja</h1>
            <button id="themeToggle" class="theme-toggle" title="Toggle theme">
                <span class="theme-icon">🌙</span>
            </button>
        </div>

        <!-- Action buttons -->
        <div class="action-buttons">
            <button id="extractColors" class="btn btn-primary">
                <span class="btn-icon">🎨</span>
                Extract Colors
            </button>
            <button id="colorPicker" class="btn btn-secondary">
                <span class="btn-icon">🎯</span>
                Color Picker
            </button>
        </div>

        <!-- Picked color display -->
        <div id="pickedColorSection" class="picked-color-section" style="display: none;">
            <h3>Picked Color</h3>
            <div id="pickedColorDisplay" class="color-item">
                <!-- Picked color will be displayed here -->
            </div>
        </div>

        <!-- Extracted colors section -->
        <div id="extractedColorsSection" class="colors-section">
            <h3>Extracted Colors</h3>
            <div id="extractedColorsList" class="colors-list">
                <p class="placeholder">Click "Extract Colors" to see colors from this page</p>
            </div>
        </div>

        <!-- Saved colors section -->
        <div class="colors-section">
            <h3>Saved Colours</h3>
            <div id="savedColorsList" class="colors-list">
                <p class="placeholder">No saved colors yet</p>
            </div>
        </div>

        <!-- Download button -->
        <div class="download-section">
            <button id="downloadJson" class="btn btn-download">
                <span class="btn-icon">📥</span>
                Download as JSON
            </button>
        </div>

        <!-- Footer -->
        <div class="footer">
            <span>Made with <span class="heart">❤️</span> by Deepak Raj</span>
            <a href="https://www.linkedin.com/in/deepak-rajj/" target="_blank" class="linkedin-link" title="LinkedIn Profile">
                <span class="linkedin-icon">💼</span>
            </a>
        </div>
    </div>

    <!-- Toast notification -->
    <div id="toast" class="toast">
        <span id="toastMessage">Copied!</span>
    </div>

    <script src="popup.js"></script>
</body>
</html>

