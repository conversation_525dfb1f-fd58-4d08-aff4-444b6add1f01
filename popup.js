// ColorNinja Popup JavaScript

class ColorNinja {
    constructor() {
        this.extractedColors = [];
        this.savedColors = this.loadSavedColors();
        this.currentTheme = this.loadTheme();
        this.currentTabId = null;
        this.init();
    }

    async init() {
        await this.getCurrentTab();
        this.setupEventListeners();
        this.applyTheme();
        this.displaySavedColors();
        await this.loadTabColors();
        this.setupMessageListener();
    }

    async getCurrentTab() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            this.currentTabId = tab.id;
            this.currentTab = tab;

            // Check if the current tab is a restricted URL
            if (this.isRestrictedUrl(tab.url)) {
                this.showRestrictedPageMessage();
            }
        } catch (error) {
            console.error('Error getting current tab:', error);
        }
    }

    isRestrictedUrl(url) {
        if (!url) return true;

        const restrictedProtocols = ['chrome:', 'chrome-extension:', 'moz-extension:', 'edge:', 'about:', 'file:'];
        const restrictedDomains = ['chrome.google.com'];

        return restrictedProtocols.some(protocol => url.startsWith(protocol)) ||
               restrictedDomains.some(domain => url.includes(domain));
    }

    showRestrictedPageMessage() {
        const extractedContainer = document.getElementById('extractedColorsList');
        extractedContainer.innerHTML = `
            <div class="restricted-message">
                <p>⚠️ ColorNinja cannot work on this page</p>
                <p>This extension doesn't work on:</p>
                <ul>
                    <li>Chrome internal pages (chrome://)</li>
                    <li>Extension pages</li>
                    <li>Chrome Web Store</li>
                    <li>Local files</li>
                </ul>
                <p>Please navigate to a regular website to use ColorNinja.</p>
            </div>
        `;

        // Disable buttons
        document.getElementById('extractColors').disabled = true;
        document.getElementById('colorPicker').disabled = true;
    }

    setupMessageListener() {
        // Listen for messages from content script
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            if (request.action === 'colorPicked') {
                this.handlePickedColor(request.colorData);
                sendResponse({ success: true });
            }
        });
    }

    setupEventListeners() {
        // Theme toggle
        document.getElementById('themeToggle').addEventListener('click', () => {
            this.toggleTheme();
        });

        // Extract colors button
        document.getElementById('extractColors').addEventListener('click', () => {
            this.extractColors();
        });

        // Color picker button
        document.getElementById('colorPicker').addEventListener('click', () => {
            this.activateColorPicker();
        });

        // Download JSON button
        document.getElementById('downloadJson').addEventListener('click', () => {
            this.downloadAsJson();
        });
    }

    // Theme management
    toggleTheme() {
        this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.applyTheme();
        this.saveTheme();
    }

    applyTheme() {
        const body = document.body;
        const themeIcon = document.querySelector('.theme-icon');
        
        if (this.currentTheme === 'dark') {
            body.classList.add('dark-mode');
            themeIcon.textContent = '☀️';
        } else {
            body.classList.remove('dark-mode');
            themeIcon.textContent = '🌙';
        }
    }

    saveTheme() {
        localStorage.setItem('colorNinjaTheme', this.currentTheme);
    }

    loadTheme() {
        return localStorage.getItem('colorNinjaTheme') || 'light';
    }

    // Tab-specific color storage
    async loadTabColors() {
        if (!this.currentTabId) return;
        
        try {
            const response = await chrome.runtime.sendMessage({
                action: 'getTabColors',
                tabId: this.currentTabId
            });
            
            if (response && response.colors) {
                this.extractedColors = response.colors;
                this.displayExtractedColors();
            }
        } catch (error) {
            console.error('Error loading tab colors:', error);
        }
    }

    async saveTabColors() {
        if (!this.currentTabId) return;
        
        try {
            await chrome.runtime.sendMessage({
                action: 'saveTabColors',
                tabId: this.currentTabId,
                colors: this.extractedColors
            });
        } catch (error) {
            console.error('Error saving tab colors:', error);
        }
    }

    // Color extraction
    async extractColors() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            // Check if URL is restricted
            if (this.isRestrictedUrl(tab.url)) {
                this.showToast('Cannot extract colors from this page type');
                return;
            }

            // Send message to content script to extract colors
            const response = await chrome.tabs.sendMessage(tab.id, { action: 'extractColors' });

            if (response && response.colors) {
                this.extractedColors = response.colors;
                this.displayExtractedColors();
                await this.saveTabColors(); // Save to tab-specific storage
            } else {
                this.showToast('No colors found on this page');
            }
        } catch (error) {
            console.error('Error extracting colors:', error);
            if (error.message.includes('Could not establish connection')) {
                this.showToast('Please refresh the page and try again');
            } else {
                this.showToast('Error extracting colors. Please refresh the page and try again.');
            }
        }
    }

    displayExtractedColors() {
        const container = document.getElementById('extractedColorsList');
        
        if (this.extractedColors.length === 0) {
            container.innerHTML = '<p class="placeholder">No colors found on this page</p>';
            return;
        }

        container.innerHTML = '';
        
        this.extractedColors.forEach((colorData, index) => {
            const colorItem = this.createColorItem(colorData, 'extracted', index);
            container.appendChild(colorItem);
        });
    }

    // Color picker
    async activateColorPicker() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            // Check if URL is restricted
            if (this.isRestrictedUrl(tab.url)) {
                this.showToast('Cannot use color picker on this page type');
                return;
            }

            // Check if EyeDropper API is supported
            if (!window.EyeDropper) {
                this.showToast('Color picker not supported in this browser');
                return;
            }

            // Send message to content script to activate color picker
            await chrome.tabs.sendMessage(tab.id, { action: 'activateColorPicker' });

            // Close popup to avoid obstruction
            window.close();
        } catch (error) {
            console.error('Error activating color picker:', error);
            if (error.message.includes('Could not establish connection')) {
                this.showToast('Please refresh the page and try again');
            } else {
                this.showToast('Error activating color picker. EyeDropper API may not be supported.');
            }
        }
    }

    // Handle picked color from content script
    handlePickedColor(colorData) {
        const pickedSection = document.getElementById('pickedColorSection');
        const pickedDisplay = document.getElementById('pickedColorDisplay');
        
        pickedSection.style.display = 'block';
        pickedDisplay.innerHTML = '';
        
        const colorItem = this.createColorItem(colorData, 'picked');
        pickedDisplay.appendChild(colorItem);
        
        this.showToast('Color picked successfully!');
    }

    // Color item creation
    createColorItem(colorData, type, index = null) {
        const item = document.createElement('div');
        item.className = 'color-item';
        
        const swatch = document.createElement('div');
        swatch.className = 'color-swatch';
        swatch.style.backgroundColor = colorData.hex;
        swatch.title = colorData.hex;
        swatch.addEventListener('click', () => {
            this.copyToClipboard(colorData.hex);
        });
        
        const info = document.createElement('div');
        info.className = 'color-info';
        
        const hex = document.createElement('div');
        hex.className = 'color-hex';
        hex.textContent = colorData.hex;
        
        const formats = document.createElement('div');
        formats.className = 'color-formats';
        
        // Add format buttons
        const formatButtons = [
            { label: 'HEX', value: colorData.hex },
            { label: 'RGB', value: colorData.rgb },
            { label: 'HSL', value: colorData.hsl }
        ];
        
        formatButtons.forEach(format => {
            const btn = document.createElement('button');
            btn.className = 'format-btn';
            btn.textContent = format.label;
            btn.addEventListener('click', () => {
                this.copyToClipboard(format.value);
            });
            formats.appendChild(btn);
        });
        
        info.appendChild(hex);
        
        // Add count for extracted colors
        if (type === 'extracted' && colorData.count) {
            const count = document.createElement('div');
            count.className = 'color-count';
            count.textContent = `Used ${colorData.count} time${colorData.count > 1 ? 's' : ''}`;
            info.appendChild(count);
        }
        
        info.appendChild(formats);
        
        const actions = document.createElement('div');
        actions.className = 'color-actions';
        
        // Save button for extracted and picked colors
        if (type === 'extracted' || type === 'picked') {
            const saveBtn = document.createElement('button');
            saveBtn.className = 'save-btn';
            saveBtn.textContent = 'Save';
            saveBtn.addEventListener('click', () => {
                this.saveColor(colorData);
            });
            actions.appendChild(saveBtn);
        }
        
        // Remove button for saved colors
        if (type === 'saved') {
            const removeBtn = document.createElement('button');
            removeBtn.className = 'remove-btn';
            removeBtn.textContent = '×';
            removeBtn.title = 'Remove color';
            removeBtn.addEventListener('click', () => {
                this.removeSavedColor(index);
            });
            actions.appendChild(removeBtn);
        }
        
        item.appendChild(swatch);
        item.appendChild(info);
        item.appendChild(actions);
        
        return item;
    }

    // Color saving and management
    saveColor(colorData) {
        // Check if color is already saved
        const exists = this.savedColors.some(saved => saved.hex === colorData.hex);
        
        if (exists) {
            this.showToast('Color already saved');
            return;
        }
        
        this.savedColors.push(colorData);
        this.saveSavedColors();
        this.displaySavedColors();
        this.showToast('Color saved!');
    }

    removeSavedColor(index) {
        this.savedColors.splice(index, 1);
        this.saveSavedColors();
        this.displaySavedColors();
        this.showToast('Color removed');
    }

    displaySavedColors() {
        const container = document.getElementById('savedColorsList');
        
        if (this.savedColors.length === 0) {
            container.innerHTML = '<p class="placeholder">No saved colors yet</p>';
            return;
        }

        container.innerHTML = '';
        
        this.savedColors.forEach((colorData, index) => {
            const colorItem = this.createColorItem(colorData, 'saved', index);
            container.appendChild(colorItem);
        });
    }

    saveSavedColors() {
        localStorage.setItem('colorNinjaSavedColors', JSON.stringify(this.savedColors));
    }

    loadSavedColors() {
        const saved = localStorage.getItem('colorNinjaSavedColors');
        return saved ? JSON.parse(saved) : [];
    }

    // Utility functions
    copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(() => {
            this.showToast('Copied!');
        }).catch(err => {
            console.error('Failed to copy:', err);
            this.showToast('Failed to copy');
        });
    }

    showToast(message) {
        const toast = document.getElementById('toast');
        const toastMessage = document.getElementById('toastMessage');
        
        toastMessage.textContent = message;
        toast.classList.add('show');
        
        setTimeout(() => {
            toast.classList.remove('show');
        }, 2000);
    }

    // Download functionality
    async downloadAsJson() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            const data = {
                url: tab.url,
                title: tab.title,
                extractedColors: this.extractedColors,
                savedColors: this.savedColors,
                timestamp: new Date().toISOString(),
                totalExtractedColors: this.extractedColors.length,
                totalSavedColors: this.savedColors.length
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `colorninja-colors-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            
            URL.revokeObjectURL(url);
            this.showToast('Downloaded!');
        } catch (error) {
            console.error('Error downloading JSON:', error);
            this.showToast('Error downloading file');
        }
    }

    // Color format conversion utilities
    static hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    }

    static rgbToHsl(r, g, b) {
        r /= 255;
        g /= 255;
        b /= 255;
        
        const max = Math.max(r, g, b);
        const min = Math.min(r, g, b);
        let h, s, l = (max + min) / 2;
        
        if (max === min) {
            h = s = 0;
        } else {
            const d = max - min;
            s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
            
            switch (max) {
                case r: h = (g - b) / d + (g < b ? 6 : 0); break;
                case g: h = (b - r) / d + 2; break;
                case b: h = (r - g) / d + 4; break;
            }
            h /= 6;
        }
        
        return {
            h: Math.round(h * 360),
            s: Math.round(s * 100),
            l: Math.round(l * 100)
        };
    }

    static formatColorData(hex) {
        const rgb = ColorNinja.hexToRgb(hex);
        if (!rgb) return null;
        
        const hsl = ColorNinja.rgbToHsl(rgb.r, rgb.g, rgb.b);
        
        return {
            hex: hex,
            rgb: `rgb(${rgb.r}, ${rgb.g}, ${rgb.b})`,
            hsl: `hsl(${hsl.h}, ${hsl.s}%, ${hsl.l}%)`
        };
    }
}

// Initialize ColorNinja when popup loads
document.addEventListener('DOMContentLoaded', () => {
    new ColorNinja();
});

